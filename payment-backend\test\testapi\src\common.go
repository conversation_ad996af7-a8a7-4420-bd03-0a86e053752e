package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 环境配置
type EnvConfig struct {
	Name        string
	ExternalURL string // 外网地址
	LocalGinURL string // 内网 gin HTTP 地址
	LocalRpcURL string // 内网 RPC 地址
}

// 测试结果
type TestResult struct {
	TestName string
	Success  bool
	Message  string
	Duration time.Duration
}

// 创建订单请求
type CreateOrderRequest struct {
	ProductID   string `json:"product_id"`
	ProductDesc string `json:"product_desc"`
	PriceID     string `json:"price_id"`
	Quantity    int    `json:"quantity"`
	PSPProvider string `json:"psp_provider"`
}

// 创建订单响应
type CreateOrderResponse struct {
	OrderID     string    `json:"order_id"`
	CheckoutURL string    `json:"checkout_url"`
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// 登录请求
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// 登录响应
type LoginResponse struct {
	Seq      int    `json:"seq"`
	Token    string `json:"token"`
	ExpireAt int64  `json:"expireAt"`
	UserID   string `json:"userId"`
}

// getEnvConfig 获取环境配置
func getEnvConfig(env string) *EnvConfig {
	switch env {
	case "dev1":
		return &EnvConfig{
			Name:        "dev1",
			ExternalURL: dev1_external_url_base,
			LocalGinURL: dev1_local_gin_url_base,
			LocalRpcURL: dev1_local_rpc_url_base,
		}
	case "dev2":
		return &EnvConfig{
			Name:        "dev2",
			ExternalURL: dev2_external_url_base,
			LocalGinURL: dev2_local_gin_url_base,
			LocalRpcURL: dev2_local_rpc_url_base,
		}
	case "sit":
		return &EnvConfig{
			Name:        "sit",
			ExternalURL: sit_external_url_base,
			LocalGinURL: sit_local_gin_url_base,
			LocalRpcURL: sit_local_rpc_url_base,
		}
	default:
		return nil
	}
}

// makeHTTPRequest 发送HTTP请求的通用函数
func makeHTTPRequest(method, url string, headers map[string]string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, err
	}

	// 设置默认头部
	req.Header.Set("Content-Type", "application/json")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{Timeout: 60 * time.Second}
	return client.Do(req)
}

// generateTraceID 生成UUID v4作为trace ID
func generateTraceID() string {
	// 使用简单的方法生成UUID，避免导入问题
	return fmt.Sprintf("test-%d-%d", time.Now().UnixNano(), time.Now().Unix())
}

// getExternalToken 获取外网接口的token
func getExternalToken(config *EnvConfig) (string, error) {
	url := config.ExternalURL + "/api/v1/user/login"

	loginReq := LoginRequest{
		Email:    "<EMAIL>",
		Password: "iscas.ac.cn@@20250724",
	}

	resp, err := makeHTTPRequest("POST", url, map[string]string{
		"x-trace-id": generateTraceID(),
	}, loginReq)

	if err != nil {
		return "", fmt.Errorf("login request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read login response: %w", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", fmt.Errorf("failed to parse login response: %w", err)
	}

	if loginResp.Token == "" {
		return "", fmt.Errorf("empty token in login response")
	}

	return loginResp.Token, nil
}

// printTestSummary 打印测试结果汇总
func printTestSummary(run_env string, results []TestResult) {
	fmt.Printf("\n=== Test Summary for %v ===", run_env)

	successCount := 0
	totalCount := len(results)

	for _, result := range results {
		status := "❌ FAIL"
		if result.Success {
			status = "✅ PASS"
			successCount++
		}

		fmt.Printf("%s %s (%.2fs) - %s\n",
			status,
			result.TestName,
			result.Duration.Seconds(),
			result.Message)
	}

	fmt.Printf("\nResults: %d/%d tests passed\n", successCount, totalCount)
	if successCount == totalCount {
		fmt.Println("🎉 All tests passed!")
	} else {
		fmt.Printf("⚠️  %d tests failed\n", totalCount-successCount)
	}
}

func prettyJson(body []byte) string {
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, body, "    ", "  "); err == nil {
		return prettyJSON.String()
	} else {
		return string(body)
	}
}
