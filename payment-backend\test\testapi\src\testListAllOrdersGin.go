package main

import (
	"fmt"
	"io"
	"time"
)

// testListAllOrdersGin 测试内网获取批量订单接口 (Gin HTTP)
func testListAllOrdersGin(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "List All Orders (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/order-service/orders?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", prettyJson(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "All orders retrieved successfully via Gin HTTP",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
