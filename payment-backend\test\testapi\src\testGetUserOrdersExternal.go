package main

import (
	"fmt"
	"io"
	"time"
)

// testGetUserOrdersExternal 测试外网获取用户订单接口
func testGetUserOrdersExternal(config *EnvConfig, token string) TestResult {
	start := time.Now()
	testName := "Get User Orders (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/order-service/orders?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id":    generateTraceID(),
		"Authorization": "Bearer " + token,
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", prettyJson(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "User orders retrieved successfully",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
