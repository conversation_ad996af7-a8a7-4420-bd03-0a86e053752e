package main

import (
	"fmt"
	"io"
	"time"
)

// testListAllOrdersRPC 测试内网获取批量订单接口 (Dubbo RPC)
func testListAllOrdersRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "List All Orders (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.OrderService/ListAllOrders"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"pagination": map[string]interface{}{
			"limit":  50,
			"offset": 0,
		},
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", prettyJson(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "All orders retrieved successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
