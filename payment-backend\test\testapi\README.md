# API 测试程序

这是一个用于测试支付后端服务API接口的自动化测试程序。

## 功能特性

- 支持多环境测试（dev1, dev2, sit）
- 自动获取外网接口的认证token
- 测试外网接口（Gin HTTP）
- 测试内网接口（Gin HTTP + Dubbo RPC）
- 详细的测试结果报告

## 测试接口

### 外网接口（只测试 Gin HTTP）
1. **创建订单** - `POST /api/v1/pay-service/order-service/orders`
2. **获取用户订单** - `GET /api/v1/pay-service/order-service/orders`

### 内网接口（测试 Gin HTTP + Dubbo RPC）
1. **获取批量订单（Gin HTTP）** - `GET /api/v1/pay-service/admin/order-service/orders`
2. **获取批量订单（Dubbo RPC）** - `POST /com.aibook.payment.grpc.OrderService/ListAllOrders`

## 环境配置

- **dev1**: 开发环境1
  - 外网: http://ny10wt9045294.vicp.fun:25639
  - 内网Gin: http://192.168.1.200:15445
  - 内网RPC: http://192.168.1.200:15446

- **dev2**: 开发环境2
  - 外网: http://ny10wt9045294.vicp.fun
  - 内网Gin: http://192.168.1.200:25906
  - 内网RPC: http://192.168.1.200:25907

- **sit**: 系统集成测试环境
  - 外网: http://ny10wt9045294.vicp.fun:29397
  - 内网Gin: http://192.168.1.200:30000
  - 内网RPC: http://192.168.1.200:30001

## 使用方法

### 1. 编译程序

```bash
# 在 payment-backend 目录下运行
build.bat
```

或者手动编译：

```bash
cd test\testapi
go build -o test_api.exe test_api.go
```

### 2. 运行测试

```bash
# 使用批处理脚本（推荐）
run_test.bat [环境名]

# 或直接运行可执行文件
cd test\testapi
.\test_api.exe [环境名]
```

### 3. 环境参数

- `dev1` - 测试开发环境1
- `dev2` - 测试开发环境2
- `sit` - 测试SIT环境
- `all` - 测试所有环境（默认）

### 示例

```bash
# 测试dev1环境
run_test.bat dev1

# 测试所有环境
run_test.bat all
# 或
run_test.bat
```

## 认证方式

### 外网接口
- 使用登录接口自动获取token
- 请求头包含：
  - `x-trace-id`: UUID v4格式的追踪ID
  - `Authorization`: Bearer token

### 内网接口
- 无需token认证
- 请求头包含：
  - `x-trace-id`: UUID v4格式的追踪ID
  - `x-user-id`: 用户ID（admin123）
  - `x-role`: 用户角色（admin）

## 测试结果

程序会显示每个测试的详细结果，包括：
- 测试名称
- 执行状态（✅ PASS / ❌ FAIL）
- 执行时间
- 响应状态码
- 响应内容
- 最终汇总统计

## 依赖

- Go 1.24+
- github.com/google/uuid

## 文件结构

```
test/testapi/
├── test_api.go      # 主程序源码
├── test_api.exe     # 编译后的可执行文件
├── go.mod           # Go模块文件
├── go.sum           # Go依赖校验文件
├── build.bat        # 编译脚本
├── run_test.bat     # 运行脚本
└── README.md        # 说明文档
```

## 注意事项

1. 确保网络连接正常，能够访问测试环境
2. 外网接口需要有效的登录凭据
3. 内网接口需要能够访问内网地址
4. 测试会创建真实的订单数据，请在测试环境中运行
