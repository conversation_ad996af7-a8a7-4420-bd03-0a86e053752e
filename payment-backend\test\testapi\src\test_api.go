package main

import (
	"fmt"
	"os"
	"strings"
)

const (
	// dev1 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev1_local_gin_url_base = "http://192.168.1.200:15445"
	// dev2 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev2_local_gin_url_base = "http://192.168.1.200:25906"
	// sit 服务器 内网 gin HTTP 地址
	sit_local_gin_url_base = "http://192.168.1.200:30000" // 端口号待定

	// dev1 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev1_local_rpc_url_base = "http://192.168.1.200:15446"
	// dev2 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev2_local_rpc_url_base = "http://192.168.1.200:25907"
	// sit 服务器 内网 RPC 地址(注意，在 k8s 上需映射)
	sit_local_rpc_url_base = "http://192.168.1.200:30001" // 端口号待定

	// dev1 服务器 外网地址
	dev1_external_url_base = "http://ny10wt9045294.vicp.fun:25639"
	// dev2 服务器 外网地址
	dev2_external_url_base = "http://ny10wt9045294.vicp.fun"
	// sit 服务器 外网地址
	sit_external_url_base = "http://ny10wt9045294.vicp.fun:29397"
)

func main() {
	run_env := "all"
	if len(os.Args) > 1 {
		run_env = os.Args[1]
	}
	if run_env == "all" {
		run_env = "dev1,dev2,sit"
	}

	run_envs := make([]string, 0)
	for _, env := range strings.Split(run_env, ",") {
		run_envs = append(run_envs, strings.TrimSpace(env))
	}

	testResults := make([][]TestResult, 0)
	testedRunEnvs := make([]string, 0)
	for _, run_env := range run_envs {
		fmt.Printf("+++++++++++++++ Starting test for %v +++++++++++++++\n", run_env)
		testResult := StartTest(run_env)
		if testResult != nil {
			testResults = append(testResults, testResult)
			testedRunEnvs = append(testedRunEnvs, run_env)
		}
		fmt.Printf("--------------- Finished test for %v ---------------\n\n", run_env)
	}

	fmt.Printf("\n\n##################### EACH TEST SUMMARY #####################\n")
	for i, testResult := range testResults {
		printTestSummary(testedRunEnvs[i], testResult)
		fmt.Println()
	}
}

func StartTest(run_env string) []TestResult {
	config := getEnvConfig(run_env)
	if config == nil {
		fmt.Printf("Unknown environment: %s\n", run_env)
		return nil
	}

	fmt.Printf("Testing environment: %s\n", config.Name)
	fmt.Printf("External URL: %s\n", config.ExternalURL)
	fmt.Printf("Local Gin URL: %s\n", config.LocalGinURL)
	fmt.Printf("Local RPC URL: %s\n", config.LocalRpcURL)
	fmt.Println()

	var results []TestResult

	// 获取外网接口的token
	fmt.Println("=== Getting External API Token ===")
	token, err := getExternalToken(config)
	if err != nil {
		fmt.Printf("Failed to get external token: %v\n", err)
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  false,
			Message:  fmt.Sprintf("Failed to get token: %v", err),
			Duration: 0,
		})
	} else {
		fmt.Printf("Token obtained successfully: %s...\n", token[:50])
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  true,
			Message:  "Token obtained successfully",
			Duration: 0,
		})
	}

	// 测试外网接口 (只测试 gin HTTP)
	fmt.Println("\n=== Testing External APIs (Gin HTTP only) ===")
	results = append(results, testCreateOrderExternal(config, token))
	results = append(results, testGetUserOrdersExternal(config, token))

	// 测试内网接口 (测试 gin HTTP 和 dubbo RPC)
	fmt.Println("\n=== Testing Internal APIs (Gin HTTP + Dubbo RPC) ===")
	results = append(results, testListAllOrdersGin(config))
	results = append(results, testListAllOrdersRPC(config))

	// 打印测试结果汇总
	printTestSummary(run_env, results)

	return results
}
