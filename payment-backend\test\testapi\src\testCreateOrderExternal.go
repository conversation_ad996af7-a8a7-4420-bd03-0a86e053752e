package main

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

// testCreateOrderExternal 测试外网创建订单接口
func testCreateOrderExternal(config *EnvConfig, token string) TestResult {
	start := time.Now()
	testName := "Create Order (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/order-service/orders"
	headers := map[string]string{
		"x-trace-id":    generateTraceID(),
		"Authorization": "Bearer " + token,
	}

	requestBody := CreateOrderRequest{
		ProductID:   "prod_stripe_001",
		ProductDesc: "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
		PriceID:     "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
		Quantity:    1,
		PSPProvider: "stripe",
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 || resp.StatusCode == 201 || resp.StatusCode == 303 {
		var orderResp CreateOrderResponse
		if err := json.Unmarshal(body, &orderResp); err == nil {
			fmt.Printf("    Order ID: %s\n", orderResp.OrderID)
			fmt.Printf("    Amount: %.2f %s\n", orderResp.Amount, orderResp.Currency)
			if orderResp.CheckoutURL != "" {
				fmt.Printf("    Checkout URL: %s...\n", orderResp.CheckoutURL[:50])
			}
		}
		fmt.Printf("    Response: %s\n", prettyJson(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Order created successfully",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
